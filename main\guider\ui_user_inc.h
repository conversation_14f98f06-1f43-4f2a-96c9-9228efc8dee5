#ifndef _UI_USER_INC_H
#define _UI_USER_INC_H

#ifdef __cplusplus
extern "C" {
#endif

#include "esp_log.h"

#include "lvgl.h"
#include "gui_guider.h"
// #include "ui_helper.h"
// #include "ui_flipnum.h"
// #include "ui_file_manager.h"
// #include "ui_toast_txt.h"
// #include "ui_keyboard_helper.h"

#include "common.h"

#define TAG_GUI "ui"

#define DEF_UI_SCR_START UI_SCR_ID_START_PAGE
#define DEF_UI_SCR_ANIMTIME 250
#define DEF_UI_SCR_ANIMTYPE LV_SCR_LOAD_ANIM_MOVE_TOP //LV_SCR_LOAD_ANIM_NONE

#define UI_SCR_DECLARE(var_name) extern ui_scr_t var_name;

void events_init_Start_Page(lv_ui *ui);
void events_init_Menu_page(lv_ui *ui);
void events_init_PID_page(lv_ui *ui);
void events_init_Can_Connet_page(lv_ui *ui);
void events_init_BT_page(lv_ui *ui);
void events_init_Settings_Page(lv_ui *ui);
void events_init_Wave_Page(lv_ui *ui);
void events_init_devoce_info_page(lv_ui *ui);
void events_init_WIFI_page(lv_ui *ui);
void events_init_update_Page(lv_ui *ui);

             
typedef enum
{
    UI_DISPLAY_SHOW = 1,
    UI_DISPLAY_HIDE = 2,
} ui_display_state_t;

typedef enum
{
    UI_NOTIF_NONE,
    UI_NOTIF_CAN_DATA_UPDATE,
    UI_NOTIF_CAN_STATUS_UPDATE,
    UI_NOTIF_WAVE_DATA_UPDATE,        // 波形数据更新
} ui_notif_type_t;

typedef enum
{
    UI_SCR_ID_START_PAGE = 1,
    UI_SCR_ID_MENU_PAGE,
    UI_SCR_ID_CAN_CONNET_PAGE,
    UI_SCR_ID_BT_PAGE,
    UI_SCR_ID_SETTINGS_PAGE,
    UI_SCR_ID_PID_PAGE,
    UI_SCR_ID_AI_PAGE,
    UI_SCR_ID_WAVE_PAGE,
    UI_SCR_ID_WIFI_PAGE,
    UI_SCR_ID_DEVOCE_INFO_PAGE,
    UI_SCR_ID_UPDATE_PAGE,
    UI_SCR_ID_MAX,

    UI_SCR_ID_ALL_MAX,
} ui_scr_id_t;

typedef struct
{
    lv_obj_t **obj;                       // 页面窗体对象
    ui_scr_id_t id;                       // 页面ID
    const char *name;                     // 页面名称
    int anim_time;                        // 页面动画时间
    lv_scr_load_anim_t load_anim;         // 页面动画
    void (*setup_handle)(lv_ui *ui);      // Guider页面初始化函数
    void (*setup_user_handle)(lv_ui *ui); // User自定义页面初始化函数
} ui_scr_t;

typedef struct
{
    ui_notif_type_t type;
    void *user_data;
} ui_notif_msg_t;

// CAN数据结构定义
typedef struct {
    int32_t voltage;      // 母线电压 (V)
    int32_t current;      // 电流 (A)
    int32_t temperature;  // 温度 (°C)
    int32_t speed;        // 速度
    int32_t position;   // 位置
    uint32_t timestamp; // 时间戳
    bool valid;         // 数据有效性标志
} can_display_data_t;

// 波形数据类型枚举 - 便于扩展新的数据类型
typedef enum {
    WAVE_DATA_CURRENT = 0,    // 电流波形
    WAVE_DATA_SPEED,          // 速度波形
    WAVE_DATA_POSITION,       // 位置波形
    WAVE_DATA_VOLTAGE,        // 速度轨迹
    WAVE_DATA_TEMPERATURE,    // 位置轨迹
    WAVE_DATA_MAX             // 最大值，用于数组大小
} wave_data_type_t;

// 波形数据结构
typedef struct {
    wave_data_type_t type;    // 数据类型
    int32_t value;            // 数据值
} wave_data_t;

typedef struct {
    bool connected;     // 连接状态
    uint32_t error_count; // 错误计数
    uint32_t msg_count;   // 消息计数
} can_status_data_t;



UI_SCR_DECLARE(def_scr_start_page)
UI_SCR_DECLARE(def_scr_menu_page)
UI_SCR_DECLARE(def_scr_can_connet_page)
UI_SCR_DECLARE(def_scr_pid_page)
UI_SCR_DECLARE(def_scr_ai_page)
UI_SCR_DECLARE(def_scr_wave_page)
UI_SCR_DECLARE(def_scr_devoce_info_page)
UI_SCR_DECLARE(def_scr_WIFI_page)
UI_SCR_DECLARE(def_scr_update_page)
UI_SCR_DECLARE(def_scr_bt_page)
UI_SCR_DECLARE(def_scr_settings_page)


void ui_scr_init(ui_scr_id_t start_id);
void ui_scr_goto(ui_scr_id_t id, bool anim);
void ui_scr_goback(bool anim);
void ui_scr_add(ui_scr_t *scr);
void ui_scr_remove(ui_scr_id_t id);
ui_scr_t *ui_scr_get(ui_scr_id_t id);
ui_scr_t *ui_scr_get_cur(void);
void ui_scr_set_animtime(ui_scr_id_t id, int time);
void ui_scr_set_animtype(ui_scr_id_t id, lv_scr_load_anim_t anim);


void ui_notif_service_init(void);
void ui_notif_service_commit(ui_notif_msg_t *msg);

extern void back_to_menu_event_handler(lv_event_t *e);
extern wave_data_type_t wave_get_display_type(void);
extern void ui_notif_scr_wave_task(ui_notif_msg_t *msg);
extern void wave_set_display_type(wave_data_type_t type);
extern wave_data_type_t wave_get_display_type(void);
extern void wave_auto_scale(void);
extern void wave_toggle_auto_scale(void);
extern void wave_increase_resolution(void);
extern void wave_decrease_resolution(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
