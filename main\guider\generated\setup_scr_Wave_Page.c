/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_Wave_Page(lv_ui *ui)
{
    //Write codes Wave_Page
    ui->Wave_Page = lv_obj_create(NULL);
    lv_obj_set_size(ui->Wave_Page, 320, 240);
    lv_obj_set_scrollbar_mode(ui->Wave_Page, LV_SCROLLBAR_MODE_OFF);

    //Write style for Wave_Page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page, lv_color_hex(0x242424), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Wave_Page_chart_1
    ui->Wave_Page_chart_1 = lv_chart_create(ui->Wave_Page);
    lv_chart_set_type(ui->Wave_Page_chart_1, LV_CHART_TYPE_LINE);
    lv_chart_set_div_line_count(ui->Wave_Page_chart_1, 33, 35);
    lv_chart_set_point_count(ui->Wave_Page_chart_1, 5);
    lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y, 0, 255);
    lv_chart_set_axis_tick(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y, 2, 1, 15, 7, true, 40);
    lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_SECONDARY_Y, 0, 100);
    lv_chart_set_zoom_x(ui->Wave_Page_chart_1, 249);
    lv_chart_set_zoom_y(ui->Wave_Page_chart_1, 255);
    lv_obj_set_style_size(ui->Wave_Page_chart_1, 0, LV_PART_INDICATOR);
    lv_obj_set_pos(ui->Wave_Page_chart_1, 29, 41);
    lv_obj_set_size(ui->Wave_Page_chart_1, 250, 195);
    lv_obj_set_scrollbar_mode(ui->Wave_Page_chart_1, LV_SCROLLBAR_MODE_ACTIVE);

    //Write style for Wave_Page_chart_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_chart_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_chart_1, 4, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Wave_Page_chart_1, 195, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Wave_Page_chart_1, lv_color_hex(0x3d1616), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Wave_Page_chart_1, LV_BORDER_SIDE_FULL | LV_BORDER_SIDE_LEFT | LV_BORDER_SIDE_RIGHT | LV_BORDER_SIDE_TOP | LV_BORDER_SIDE_BOTTOM, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_chart_1, 2, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_width(ui->Wave_Page_chart_1, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->Wave_Page_chart_1, lv_color_hex(0x2c2c2c), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->Wave_Page_chart_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_chart_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Wave_Page_chart_1, Part: LV_PART_TICKS, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Wave_Page_chart_1, lv_color_hex(0xffffff), LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_chart_1, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_chart_1, 255, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_line_width(ui->Wave_Page_chart_1, 1, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->Wave_Page_chart_1, lv_color_hex(0x3f3c3c), LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->Wave_Page_chart_1, 255, LV_PART_TICKS|LV_STATE_DEFAULT);

    //Write codes Wave_Page_imgbtn_1
    ui->Wave_Page_imgbtn_1 = lv_imgbtn_create(ui->Wave_Page);
    lv_obj_add_flag(ui->Wave_Page_imgbtn_1, LV_OBJ_FLAG_CHECKABLE);
    lv_imgbtn_set_src(ui->Wave_Page_imgbtn_1, LV_IMGBTN_STATE_RELEASED, NULL, &_back01_ico_alpha_29x30, NULL);
    ui->Wave_Page_imgbtn_1_label = lv_label_create(ui->Wave_Page_imgbtn_1);
    lv_label_set_text(ui->Wave_Page_imgbtn_1_label, "");
    lv_label_set_long_mode(ui->Wave_Page_imgbtn_1_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Wave_Page_imgbtn_1_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Wave_Page_imgbtn_1, 0, LV_STATE_DEFAULT);
    lv_obj_set_pos(ui->Wave_Page_imgbtn_1, 5, 5);
    lv_obj_set_size(ui->Wave_Page_imgbtn_1, 29, 30);

    //Write style for Wave_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Wave_Page_imgbtn_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_imgbtn_1, &lv_font_montserratMedium_32, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Wave_Page_imgbtn_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_clip_corner(ui->Wave_Page_imgbtn_1, true, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Wave_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_PRESSED.
    lv_obj_set_style_img_recolor_opa(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_img_opa(ui->Wave_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_color(ui->Wave_Page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_font(ui->Wave_Page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_text_opa(ui->Wave_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_PRESSED);
    lv_obj_set_style_shadow_width(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style for Wave_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_STATE_CHECKED.
    lv_obj_set_style_img_recolor_opa(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_img_opa(ui->Wave_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_color(ui->Wave_Page_imgbtn_1, lv_color_hex(0xFF33FF), LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_font(ui->Wave_Page_imgbtn_1, &lv_font_montserratMedium_10, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_text_opa(ui->Wave_Page_imgbtn_1, 255, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_obj_set_style_shadow_width(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_STATE_CHECKED);

    //Write style for Wave_Page_imgbtn_1, Part: LV_PART_MAIN, State: LV_IMGBTN_STATE_RELEASED.
    lv_obj_set_style_img_recolor_opa(ui->Wave_Page_imgbtn_1, 0, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);
    lv_obj_set_style_img_opa(ui->Wave_Page_imgbtn_1, 255, LV_PART_MAIN|LV_IMGBTN_STATE_RELEASED);

    //Write codes Wave_Page_ddlist_1
    ui->Wave_Page_ddlist_1 = lv_dropdown_create(ui->Wave_Page);
    lv_dropdown_set_options(ui->Wave_Page_ddlist_1, "力矩环\n速度环\n位置环\n速度轨迹环\n位置轨迹环");
    lv_obj_set_pos(ui->Wave_Page_ddlist_1, 44, 5);
    lv_obj_set_size(ui->Wave_Page_ddlist_1, 79, 27);

    //Write style for Wave_Page_ddlist_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->Wave_Page_ddlist_1, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_ddlist_1, &lv_font_HarmonyOS_Sans_SC_Medium_10, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_ddlist_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_ddlist_1, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->Wave_Page_ddlist_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->Wave_Page_ddlist_1, lv_color_hex(0xb9b4b4), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->Wave_Page_ddlist_1, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->Wave_Page_ddlist_1, 8, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->Wave_Page_ddlist_1, 6, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->Wave_Page_ddlist_1, 6, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_ddlist_1, 3, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->Wave_Page_ddlist_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_ddlist_1, lv_color_hex(0x000000), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_ddlist_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_ddlist_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_CHECKED for &style_Wave_Page_ddlist_1_extra_list_selected_checked
    static lv_style_t style_Wave_Page_ddlist_1_extra_list_selected_checked;
    ui_init_style(&style_Wave_Page_ddlist_1_extra_list_selected_checked);

    lv_style_set_border_width(&style_Wave_Page_ddlist_1_extra_list_selected_checked, 1);
    lv_style_set_border_opa(&style_Wave_Page_ddlist_1_extra_list_selected_checked, 255);
    lv_style_set_border_color(&style_Wave_Page_ddlist_1_extra_list_selected_checked, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_Wave_Page_ddlist_1_extra_list_selected_checked, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_Wave_Page_ddlist_1_extra_list_selected_checked, 2);
    lv_style_set_bg_opa(&style_Wave_Page_ddlist_1_extra_list_selected_checked, 255);
    lv_style_set_bg_color(&style_Wave_Page_ddlist_1_extra_list_selected_checked, lv_color_hex(0x00a1b5));
    lv_style_set_bg_grad_dir(&style_Wave_Page_ddlist_1_extra_list_selected_checked, LV_GRAD_DIR_NONE);
    lv_obj_add_style(lv_dropdown_get_list(ui->Wave_Page_ddlist_1), &style_Wave_Page_ddlist_1_extra_list_selected_checked, LV_PART_SELECTED|LV_STATE_CHECKED);

    //Write style state: LV_STATE_DEFAULT for &style_Wave_Page_ddlist_1_extra_list_main_default
    static lv_style_t style_Wave_Page_ddlist_1_extra_list_main_default;
    ui_init_style(&style_Wave_Page_ddlist_1_extra_list_main_default);

    lv_style_set_max_height(&style_Wave_Page_ddlist_1_extra_list_main_default, 180);
    lv_style_set_text_color(&style_Wave_Page_ddlist_1_extra_list_main_default, lv_color_hex(0x0D3055));
    lv_style_set_text_font(&style_Wave_Page_ddlist_1_extra_list_main_default, &lv_font_HarmonyOS_Sans_SC_Medium_10);
    lv_style_set_text_opa(&style_Wave_Page_ddlist_1_extra_list_main_default, 255);
    lv_style_set_border_width(&style_Wave_Page_ddlist_1_extra_list_main_default, 1);
    lv_style_set_border_opa(&style_Wave_Page_ddlist_1_extra_list_main_default, 255);
    lv_style_set_border_color(&style_Wave_Page_ddlist_1_extra_list_main_default, lv_color_hex(0xe1e6ee));
    lv_style_set_border_side(&style_Wave_Page_ddlist_1_extra_list_main_default, LV_BORDER_SIDE_FULL);
    lv_style_set_radius(&style_Wave_Page_ddlist_1_extra_list_main_default, 2);
    lv_style_set_bg_opa(&style_Wave_Page_ddlist_1_extra_list_main_default, 255);
    lv_style_set_bg_color(&style_Wave_Page_ddlist_1_extra_list_main_default, lv_color_hex(0xffffff));
    lv_style_set_bg_grad_dir(&style_Wave_Page_ddlist_1_extra_list_main_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(lv_dropdown_get_list(ui->Wave_Page_ddlist_1), &style_Wave_Page_ddlist_1_extra_list_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_DEFAULT for &style_Wave_Page_ddlist_1_extra_list_scrollbar_default
    static lv_style_t style_Wave_Page_ddlist_1_extra_list_scrollbar_default;
    ui_init_style(&style_Wave_Page_ddlist_1_extra_list_scrollbar_default);

    lv_style_set_radius(&style_Wave_Page_ddlist_1_extra_list_scrollbar_default, 2);
    lv_style_set_bg_opa(&style_Wave_Page_ddlist_1_extra_list_scrollbar_default, 255);
    lv_style_set_bg_color(&style_Wave_Page_ddlist_1_extra_list_scrollbar_default, lv_color_hex(0x00ff00));
    lv_style_set_bg_grad_dir(&style_Wave_Page_ddlist_1_extra_list_scrollbar_default, LV_GRAD_DIR_NONE);
    lv_obj_add_style(lv_dropdown_get_list(ui->Wave_Page_ddlist_1), &style_Wave_Page_ddlist_1_extra_list_scrollbar_default, LV_PART_SCROLLBAR|LV_STATE_DEFAULT);

    //Write codes Wave_Page_btn_smaller
    ui->Wave_Page_btn_smaller = lv_btn_create(ui->Wave_Page);
    ui->Wave_Page_btn_smaller_label = lv_label_create(ui->Wave_Page_btn_smaller);
    lv_label_set_text(ui->Wave_Page_btn_smaller_label, "-");
    lv_label_set_long_mode(ui->Wave_Page_btn_smaller_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Wave_Page_btn_smaller_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Wave_Page_btn_smaller, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->Wave_Page_btn_smaller_label, LV_PCT(100));
    lv_obj_set_pos(ui->Wave_Page_btn_smaller, 213, 5);
    lv_obj_set_size(ui->Wave_Page_btn_smaller, 30, 30);

    //Write style for Wave_Page_btn_smaller, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_btn_smaller, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_btn_smaller, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_btn_smaller, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_btn_smaller, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_btn_smaller, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_btn_smaller, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Wave_Page_btn_smaller, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_btn_smaller, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_btn_smaller, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Wave_Page_btn_smaller, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Wave_Page_btn_biggeer
    ui->Wave_Page_btn_biggeer = lv_btn_create(ui->Wave_Page);
    ui->Wave_Page_btn_biggeer_label = lv_label_create(ui->Wave_Page_btn_biggeer);
    lv_label_set_text(ui->Wave_Page_btn_biggeer_label, "+");
    lv_label_set_long_mode(ui->Wave_Page_btn_biggeer_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Wave_Page_btn_biggeer_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Wave_Page_btn_biggeer, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->Wave_Page_btn_biggeer_label, LV_PCT(100));
    lv_obj_set_pos(ui->Wave_Page_btn_biggeer, 175, 5);
    lv_obj_set_size(ui->Wave_Page_btn_biggeer, 30, 30);

    //Write style for Wave_Page_btn_biggeer, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_btn_biggeer, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_btn_biggeer, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_btn_biggeer, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_btn_biggeer, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_btn_biggeer, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_btn_biggeer, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Wave_Page_btn_biggeer, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_btn_biggeer, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_btn_biggeer, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Wave_Page_btn_biggeer, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Wave_Page_btn_reset
    ui->Wave_Page_btn_reset = lv_btn_create(ui->Wave_Page);
    ui->Wave_Page_btn_reset_label = lv_label_create(ui->Wave_Page_btn_reset);
    lv_label_set_text(ui->Wave_Page_btn_reset_label, "R");
    lv_label_set_long_mode(ui->Wave_Page_btn_reset_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Wave_Page_btn_reset_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Wave_Page_btn_reset, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->Wave_Page_btn_reset_label, LV_PCT(100));
    lv_obj_set_pos(ui->Wave_Page_btn_reset, 251, 5);
    lv_obj_set_size(ui->Wave_Page_btn_reset, 30, 30);

    //Write style for Wave_Page_btn_reset, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_btn_reset, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_btn_reset, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_btn_reset, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_btn_reset, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_btn_reset, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_btn_reset, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Wave_Page_btn_reset, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_btn_reset, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_btn_reset, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Wave_Page_btn_reset, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Wave_Page_btn_auto
    ui->Wave_Page_btn_auto = lv_btn_create(ui->Wave_Page);
    ui->Wave_Page_btn_auto_label = lv_label_create(ui->Wave_Page_btn_auto);
    lv_label_set_text(ui->Wave_Page_btn_auto_label, "A");
    lv_label_set_long_mode(ui->Wave_Page_btn_auto_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Wave_Page_btn_auto_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Wave_Page_btn_auto, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->Wave_Page_btn_auto_label, LV_PCT(100));
    lv_obj_set_pos(ui->Wave_Page_btn_auto, 136, 5);
    lv_obj_set_size(ui->Wave_Page_btn_auto, 30, 30);

    //Write style for Wave_Page_btn_auto, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_btn_auto, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x2195f6), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_btn_auto, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_btn_auto, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_btn_auto, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_btn_auto, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Wave_Page_btn_auto, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_btn_auto, &lv_font_montserratMedium_16, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_btn_auto, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Wave_Page_btn_auto, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes Wave_Page_slider_target
    ui->Wave_Page_slider_target = lv_slider_create(ui->Wave_Page);
    lv_slider_set_range(ui->Wave_Page_slider_target, 0, 100);
    lv_slider_set_mode(ui->Wave_Page_slider_target, LV_SLIDER_MODE_NORMAL);
    lv_slider_set_value(ui->Wave_Page_slider_target, 50, LV_ANIM_OFF);
    lv_obj_set_pos(ui->Wave_Page_slider_target, 297, 29);
    lv_obj_set_size(ui->Wave_Page_slider_target, 8, 171);

    //Write style for Wave_Page_slider_target, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_slider_target, 61, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_slider_target, lv_color_hex(0x00ff7e), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_slider_target, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_slider_target, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(ui->Wave_Page_slider_target, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_slider_target, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for Wave_Page_slider_target, Part: LV_PART_INDICATOR, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_slider_target, 255, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_slider_target, lv_color_hex(0x34ff98), LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_slider_target, LV_GRAD_DIR_NONE, LV_PART_INDICATOR|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_slider_target, 50, LV_PART_INDICATOR|LV_STATE_DEFAULT);

    //Write style for Wave_Page_slider_target, Part: LV_PART_KNOB, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_slider_target, 255, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->Wave_Page_slider_target, lv_color_hex(0x00ed5c), LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->Wave_Page_slider_target, LV_GRAD_DIR_NONE, LV_PART_KNOB|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_slider_target, 50, LV_PART_KNOB|LV_STATE_DEFAULT);

    //Write codes Wave_Page_btn_send
    ui->Wave_Page_btn_send = lv_btn_create(ui->Wave_Page);
    ui->Wave_Page_btn_send_label = lv_label_create(ui->Wave_Page_btn_send);
    lv_label_set_text(ui->Wave_Page_btn_send_label, "1000");
    lv_label_set_long_mode(ui->Wave_Page_btn_send_label, LV_LABEL_LONG_WRAP);
    lv_obj_align(ui->Wave_Page_btn_send_label, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_pad_all(ui->Wave_Page_btn_send, 0, LV_STATE_DEFAULT);
    lv_obj_set_width(ui->Wave_Page_btn_send_label, LV_PCT(100));
    lv_obj_set_pos(ui->Wave_Page_btn_send, 277, 201);
    lv_obj_set_size(ui->Wave_Page_btn_send, 45, 40);

    //Write style for Wave_Page_btn_send, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->Wave_Page_btn_send, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->Wave_Page_btn_send, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->Wave_Page_btn_send, 5, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->Wave_Page_btn_send, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui->Wave_Page_btn_send, &_kmbg_45x40, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(ui->Wave_Page_btn_send, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_recolor_opa(ui->Wave_Page_btn_send, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->Wave_Page_btn_send, lv_color_hex(0xffffff), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->Wave_Page_btn_send, &lv_font_montserratMedium_11, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->Wave_Page_btn_send, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->Wave_Page_btn_send, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);

    //The custom code of Wave_Page.


    //Update current screen layout.
    lv_obj_update_layout(ui->Wave_Page);


}
