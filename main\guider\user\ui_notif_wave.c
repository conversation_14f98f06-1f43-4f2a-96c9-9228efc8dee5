#include "ui_user_inc.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "ui_wave";
#define WAVE_BUFFER_SIZE 50        // 缓存数据点数量

// 波形数据缓存
static int32_t wave_data_buffer[WAVE_DATA_MAX][WAVE_BUFFER_SIZE];
static uint16_t wave_buffer_index[WAVE_DATA_MAX] = {0};
static wave_data_type_t current_wave_type = WAVE_DATA_CURRENT; // 当前显示的波形类型

// 自动缩放和分辨率控制
static bool auto_scale_enabled = false;  // 自动缩放开关状态
static float y_axis_scale_factor = 1.0f; // Y轴缩放因子（分辨率控制）
#define MIN_SCALE_FACTOR 0.1f            // 最小缩放因子（最大范围）
#define MAX_SCALE_FACTOR 10.0f           // 最大缩放因子（最小范围）
#define SCALE_STEP 0.2f                  // 缩放步长
static volatile bool chart_updating = false;  // 图表更新标志

// 智能自动缩放参数
#define AUTO_SCALE_THRESHOLD_LOW 0.4f    // 数据占用显示范围小于40%时放大
#define AUTO_SCALE_THRESHOLD_HIGH 0.9f   // 数据占用显示范围大于90%时缩小
#define AUTO_SCALE_MARGIN_PERCENT 0.2f   // 自动缩放时的边距百分比
static int32_t current_display_min = 0;  // 当前显示的Y轴最小值
static int32_t current_display_max = 100; // 当前显示的Y轴最大值
static uint32_t auto_scale_cooldown = 0;  // 自动缩放冷却计数器
#define AUTO_SCALE_COOLDOWN_PERIOD 100   // 自动缩放冷却周期（数据点数）

static void wave_apply_y_axis_scale(void);
void wave_set_display_range(int32_t min_val, int32_t max_val);
bool wave_calculate_data_range(wave_data_type_t type, int32_t *min_val, int32_t *max_val);
static bool wave_should_auto_scale(int32_t data_min, int32_t data_max, float *scale_factor);
static bool wave_check_immediate_scale_needed(int32_t value);

// 波形配置表 - 便于扩展新的数据类型
typedef struct {
    wave_data_type_t type;
    const char *name;
    lv_color_t color;
    int32_t min_range;
    int32_t max_range;
    float scale_factor;  // 缩放因子
} wave_config_t;

static const wave_config_t wave_configs[WAVE_DATA_MAX] = {
    [WAVE_DATA_CURRENT] = {
        .type = WAVE_DATA_CURRENT,
        .name = "电流波形",
        .color = LV_COLOR_MAKE(255, 0, 52),  // 红色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f
    },
    [WAVE_DATA_SPEED] = {
        .type = WAVE_DATA_SPEED,
        .name = "速度波形",
        .color = LV_COLOR_MAKE(47, 53, 218), // 蓝色
        .min_range = -500,
        .max_range = 500,
        .scale_factor = 0.1f  
    },
    [WAVE_DATA_POSITION] = {
        .type = WAVE_DATA_POSITION,
        .name = "位置波形",
        .color = LV_COLOR_MAKE(0, 255, 0),   // 绿色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f 
    },
    [WAVE_DATA_VOLTAGE] = {
        .type = WAVE_DATA_VOLTAGE,
        .name = "电压波形",
        .color = LV_COLOR_MAKE(255, 165, 0), // 橙色
        .min_range = 0,
        .max_range = 150,
        .scale_factor = 1.0f
    },
    [WAVE_DATA_TEMPERATURE] = {
        .type = WAVE_DATA_TEMPERATURE,
        .name = "温度波形",
        .color = LV_COLOR_MAKE(255, 0, 255), // 紫色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f
    }
};

/**
 * @brief 添加波形数据到缓存
 */
static void wave_add_data_to_buffer(wave_data_type_t type, int32_t value)
{
    if (type >= WAVE_DATA_MAX) {
        return;
    }

    // 应用缩放因子
    int32_t scaled_value = (int32_t)(value * wave_configs[type].scale_factor);

    // 添加到缓存
    wave_data_buffer[type][wave_buffer_index[type]] = scaled_value;
    wave_buffer_index[type] = (wave_buffer_index[type] + 1) % WAVE_BUFFER_SIZE;
}

/**
 * @brief 更新图表显示
 */
static void wave_update_chart_display(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || !ui->Wave_Page_chart_1_series || current_wave_type >= WAVE_DATA_MAX) {
        ESP_LOGW(TAG, "Chart or series not available for display update");
        return;
    }

    const wave_config_t *config = &wave_configs[current_wave_type];

    // 设置图表范围，应用Y轴缩放
    wave_apply_y_axis_scale();

    // 清空现有数据，从头开始显示 - 修复：使用正确的数据系列指针
    lv_chart_set_all_value(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series, LV_CHART_POINT_NONE);

    // 更新数据系列颜色 - 修复：使用正确的数据系列指针
    lv_chart_set_series_color(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series, config->color);

    // 优化刷新策略：只在必要时刷新
    lv_obj_invalidate(ui->Wave_Page_chart_1);
}

/**
 * @brief 设置当前显示的波形类型
 */
void wave_set_display_type(wave_data_type_t type)
{
    if (type >= WAVE_DATA_MAX) {
        return;
    }
    current_wave_type = type;
    wave_update_chart_display();

    ESP_LOGI(TAG, "Switch to wave type: %s", wave_configs[type].name);
}

/**
 * @brief 切换自动缩放开关状态
 */
void wave_toggle_auto_scale(void)
{
    lv_ui *ui = &guider_ui;

    auto_scale_enabled = !auto_scale_enabled;

    // 更新按键颜色
    if (auto_scale_enabled) {
        // 开启状态：绿色
        lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x00FF00), LV_STATE_DEFAULT);
        ESP_LOGI(TAG, "Auto scale enabled");
    } else {
        // 关闭状态：蓝色
        lv_obj_set_style_bg_color(ui->Wave_Page_btn_auto, lv_color_hex(0x0000FF), LV_STATE_DEFAULT);
        ESP_LOGI(TAG, "Auto scale disabled");
    }
}

/**
 * @brief 增加Y轴分辨率（缩小Y轴显示范围）- 优化版本
 */
void wave_increase_resolution(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || current_wave_type >= WAVE_DATA_MAX) {
        return;
    }

    // 计算当前数据的实际范围
    int32_t data_min, data_max;
    bool has_data = wave_calculate_data_range(current_wave_type, &data_min, &data_max);

    if (!has_data) {
        // 没有数据时使用配置的默认范围
        const wave_config_t *config = &wave_configs[current_wave_type];
        data_min = config->min_range;
        data_max = config->max_range;
    }

    // 计算新的显示范围（缩小20%）
    int32_t current_range = current_display_max - current_display_min;
    int32_t new_range = (int32_t)(current_range * 0.8f); // 缩小到80%

    // 确保最小范围
    if (new_range < 10) {
        new_range = 10;
    }

    // 以数据中心为基准计算新的显示范围
    int32_t data_center = (data_max + data_min) / 2;
    int32_t new_min = data_center - new_range / 2;
    int32_t new_max = data_center + new_range / 2;

    // 更新显示范围
    wave_set_display_range(new_min, new_max);

    ESP_LOGI(TAG, "Y-axis resolution increased: [%ld, %ld] -> [%ld, %ld]",
             current_display_min, current_display_max, new_min, new_max);
}

/**
 * @brief 减少Y轴分辨率（扩大Y轴显示范围）- 优化版本
 */
void wave_decrease_resolution(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || current_wave_type >= WAVE_DATA_MAX) {
        return;
    }

    // 计算当前数据的实际范围
    int32_t data_min, data_max;
    bool has_data = wave_calculate_data_range(current_wave_type, &data_min, &data_max);

    if (!has_data) {
        // 没有数据时使用配置的默认范围
        const wave_config_t *config = &wave_configs[current_wave_type];
        data_min = config->min_range;
        data_max = config->max_range;
    }

    // 计算新的显示范围（扩大25%）
    int32_t current_range = current_display_max - current_display_min;
    int32_t new_range = (int32_t)(current_range * 1.25f); // 扩大到125%

    // 限制最大范围
    const wave_config_t *config = &wave_configs[current_wave_type];
    int32_t max_possible_range = (config->max_range - config->min_range) * 2;
    if (new_range > max_possible_range) {
        new_range = max_possible_range;
    }

    // 以数据中心为基准计算新的显示范围
    int32_t data_center = (data_max + data_min) / 2;
    int32_t new_min = data_center - new_range / 2;
    int32_t new_max = data_center + new_range / 2;

    // 更新显示范围
    wave_set_display_range(new_min, new_max);

    ESP_LOGI(TAG, "Y-axis resolution decreased: [%ld, %ld] -> [%ld, %ld]",
             current_display_min, current_display_max, new_min, new_max);
}

/**
 * @brief 设置显示范围的统一接口
 */
void wave_set_display_range(int32_t min_val, int32_t max_val)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || current_wave_type >= WAVE_DATA_MAX) {
        return;
    }

    // 确保范围有效
    if (max_val <= min_val) {
        max_val = min_val + 10;
    }

    // 更新当前显示范围
    current_display_min = min_val;
    current_display_max = max_val;

    // 设置图表Y轴范围
    lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y, min_val, max_val);

    // 重置滚动位置到中心
    int32_t center = (min_val + max_val) / 2;
    lv_obj_scroll_to(ui->Wave_Page_chart_1, 0, center, LV_ANIM_ON);

    // 刷新图表
    lv_chart_refresh(ui->Wave_Page_chart_1);
    lv_obj_invalidate(ui->Wave_Page_chart_1);

    // ESP_LOGI(TAG, "Display range updated: [%ld, %ld]", min_val, max_val);
}

/**
 * @brief 应用Y轴缩放到图表（保留兼容性）
 */
static void wave_apply_y_axis_scale(void)
{
    const wave_config_t *config = &wave_configs[current_wave_type];

    // 计算缩放后的Y轴范围
    int32_t range = config->max_range - config->min_range;
    int32_t scaled_range = (int32_t)(range / y_axis_scale_factor);

    // 确保最小范围
    if (scaled_range < 10) {
        scaled_range = 10;
    }

    // 计算新的最小值和最大值（以原始范围中心为基准）
    int32_t center = (config->max_range + config->min_range) / 2;
    int32_t new_min = center - scaled_range / 2;
    int32_t new_max = center + scaled_range / 2;

    // 使用统一的设置接口
    wave_set_display_range(new_min, new_max);
}

/**
 * @brief 获取当前显示的波形类型
 */
wave_data_type_t wave_get_display_type(void)
{
    return current_wave_type;
}

/**
 * @brief 获取波形配置
 */
const wave_config_t* wave_get_config(wave_data_type_t type)
{
    if (type >= WAVE_DATA_MAX) {
        return NULL;
    }
    return &wave_configs[type];
}

/**
 * @brief 计算当前波形数据的实际范围
 * @param type 波形数据类型
 * @param min_val 输出最小值
 * @param max_val 输出最大值
 * @return true 如果有有效数据，false 如果没有数据
 */
bool wave_calculate_data_range(wave_data_type_t type, int32_t *min_val, int32_t *max_val)
{
    if (type >= WAVE_DATA_MAX || !min_val || !max_val) {
        return false;
    }

    // 检查是否有数据
    uint16_t buffer_index = wave_buffer_index[type];
    if (buffer_index == 0) {
        // 没有数据，使用配置的默认范围
        *min_val = wave_configs[type].min_range;
        *max_val = wave_configs[type].max_range;
        return false;
    }

    // 初始化最大最小值
    int32_t min = wave_data_buffer[type][0];
    int32_t max = wave_data_buffer[type][0];

    // 遍历所有有效数据点
    uint16_t data_count = (buffer_index >= WAVE_BUFFER_SIZE) ? WAVE_BUFFER_SIZE : buffer_index;
    for (uint16_t i = 0; i < data_count; i++) {
        int32_t value = wave_data_buffer[type][i];
        if (value < min) min = value;
        if (value > max) max = value;
    }

    *min_val = min;
    *max_val = max;

    ESP_LOGI(TAG, "Data range for type %d: min=%ld, max=%ld, count=%d", type, min, max, data_count);
    return true;
}

/**
 * @brief 检查单个数据点是否需要立即缩放
 */
static bool wave_check_immediate_scale_needed(int32_t value)
{
    // 检查数据是否超出当前显示范围
    if (value < current_display_min || value > current_display_max) {
        return true;
    }

    // 检查数据是否接近边界（预防性缩放）
    int32_t display_range = current_display_max - current_display_min;
    int32_t margin = display_range / 10; // 10%的边距

    if (value < (current_display_min + margin) || value > (current_display_max - margin)) {
        // 数据接近边界，但不立即缩放，而是标记需要关注
        return false; // 暂时不启用预防性缩放，避免过于敏感
    }

    return false;
}

/**
 * @brief 智能自动缩放策略 - 优化版本
 */
static bool wave_should_auto_scale(int32_t data_min, int32_t data_max, float *scale_factor)
{
    // 计算数据范围和显示范围
    int32_t data_range = data_max - data_min;
    int32_t display_range = current_display_max - current_display_min;

    if (data_range <= 0 || display_range <= 0) {
        return false;
    }

    // 检查是否需要扩大（数据超出当前显示范围）- 立即响应，无冷却期
    if (data_min < current_display_min || data_max > current_display_max) {
        *scale_factor = 1.5f; // 扩大50%
        auto_scale_cooldown = AUTO_SCALE_COOLDOWN_PERIOD; // 设置冷却期防止后续频繁调整
        ESP_LOGI(TAG, "Immediate auto scale triggered: data [%ld, %ld] out of display range [%ld, %ld]",
                 data_min, data_max, current_display_min, current_display_max);
        return true;
    }

    // 对于优化性缩放（非紧急情况），检查冷却期
    if (auto_scale_cooldown > 0) {
        auto_scale_cooldown--;
        return false;
    }

    // 计算数据占用显示范围的百分比
    float data_usage_ratio = (float)data_range / (float)display_range;

    // 检查是否需要缩小（数据占用范围太小）- 这是优化性操作，需要冷却期
    if (data_usage_ratio < AUTO_SCALE_THRESHOLD_LOW) {
        *scale_factor = 0.7f; // 缩小30%
        auto_scale_cooldown = AUTO_SCALE_COOLDOWN_PERIOD;
        ESP_LOGI(TAG, "Optimization auto scale triggered: data usage %.1f%% < %.1f%%",
                 data_usage_ratio * 100, AUTO_SCALE_THRESHOLD_LOW * 100);
        return true;
    }

    return false;
}

/**
 * @brief 执行波形自动缩放，使波形居中显示 - 优化版本
 */
void wave_auto_scale(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || !ui->Wave_Page_chart_1_series || current_wave_type >= WAVE_DATA_MAX) {
        ESP_LOGW(TAG, "Chart or series not available for auto scale");
        return;
    }

    int32_t data_min, data_max;
    bool has_data = wave_calculate_data_range(current_wave_type, &data_min, &data_max);

    if (!has_data) {
        // 没有数据时使用默认配置范围
        const wave_config_t *config = &wave_configs[current_wave_type];
        data_min = config->min_range;
        data_max = config->max_range;
    }

    // 如果最大值和最小值相同，添加一些范围
    if (data_max == data_min) {
        if (data_max == 0) {
            data_min = -10;
            data_max = 10;
        } else {
            int32_t margin = abs(data_max) / 10 + 1; // 10%的边距，最少1
            data_min = data_max - margin;
            data_max = data_max + margin;
        }
    }

    // 检查是否需要自动缩放
    float scale_factor;
    if (!wave_should_auto_scale(data_min, data_max, &scale_factor)) {
        return; // 不需要缩放
    }

    // 计算新的显示范围
    int32_t data_center = (data_max + data_min) / 2;
    int32_t data_range = data_max - data_min;

    // 添加边距
    int32_t margin = (int32_t)(data_range * AUTO_SCALE_MARGIN_PERCENT);
    if (margin < 1) margin = 1;

    int32_t new_range = (int32_t)((data_range + 2 * margin) * scale_factor);
    int32_t new_min = data_center - new_range / 2;
    int32_t new_max = data_center + new_range / 2;

    // 使用统一的设置接口
    wave_set_display_range(new_min, new_max);

    ESP_LOGI(TAG, "Smart auto scale completed: data [%ld, %ld] -> display [%ld, %ld]",
             data_min, data_max, new_min, new_max);
}

/**
 * @brief 波形数据更新处理
 */
void ui_scr_wave_data_update(ui_notif_msg_t *msg)
{

    if (!msg || !msg->user_data) {
        return;
    }

    wave_data_t *wave_data = (wave_data_t *)msg->user_data;
    
    // 添加数据到缓存
    wave_add_data_to_buffer(wave_data->type, wave_data->value);

    // 如果是当前显示的数据类型，更新图表
    if (wave_data->type == current_wave_type) {
        lv_ui *ui = &guider_ui;
        if (ui->Wave_Page_chart_1 && ui->Wave_Page_chart_1_series) {
            int32_t scaled_value = (int32_t)(wave_data->value * wave_configs[wave_data->type].scale_factor);
            // 快速更新数据，减少在锁内的时间 - 修复：使用正确的数据系列指针
            lv_chart_set_next_value(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_series, scaled_value);

            // 如果启用了自动缩放，智能执行自动缩放
            if (auto_scale_enabled) {
                // 检查当前数据是否需要立即缩放
                if (wave_check_immediate_scale_needed(scaled_value)) {
                    // 数据超出范围，立即触发自动缩放
                    wave_auto_scale();
                    // ESP_LOGI(TAG, "Immediate auto scale triggered: value %ld out of range [%ld, %ld]",
                    //          scaled_value, current_display_min, current_display_max);
                } else {
                    // 数据在范围内，定期检查是否需要优化显示范围
                    static uint32_t auto_scale_counter = 0;
                    auto_scale_counter++;
                    // 每50个数据点检查一次是否需要优化缩放（降低频率避免过度优化）
                    if (auto_scale_counter >= 50) {
                        auto_scale_counter = 0;
                        wave_auto_scale();
                    }
                }
            }

            // 减少刷新频率：每5个数据点刷新一次
            static uint32_t refresh_counter = 0;
            refresh_counter++;
            if (refresh_counter >= 5) {
                refresh_counter = 0;
                lv_obj_invalidate(ui->Wave_Page_chart_1);
            }

        } else {
            ESP_LOGW(TAG, "Chart or series not available for data update");
        }
    }
}

void ui_notif_scr_wave_task(ui_notif_msg_t *msg)
{
    switch ((int)msg->type)
    {
    case UI_NOTIF_WAVE_DATA_UPDATE:
        ui_scr_wave_data_update(msg);
        break;

    default:
        break;
    }
}